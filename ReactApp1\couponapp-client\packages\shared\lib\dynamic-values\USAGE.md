# Expression Variables Usage Guide

## Overview
Expression variables allow you to use context-aware variables in dynamic value expressions. Instead of hardcoding widget IDs, you can use variables like `@currentGameId` that are automatically resolved from context.

## Basic Usage

### In Text Widgets
```typescript
import { useDynamicExpressionWithVariables } from '@repo/shared/lib/dynamic-values';

function ScoreDisplay() {
  // This will resolve @currentGameId to the actual widget ID
  const scoreText = useDynamicExpressionWithVariables(
    "Your score: {game/@currentGameId/score}",
    "0"
  );
  
  return <div>{scoreText}</div>;
}
```

### Available Variables
- `@currentGameId` - Current game widget's ID
- `@currentWidgetId` - Alias for @currentGameId
- `@gameType` - Game type (e.g., 'quiz-game', 'memory-game')

## Context Stacking
Expression variable contexts can be nested and will merge variables:

```typescript
<ExpressionVariableContextProvider variables={{ '@sceneId': 'scene123' }}>
  <ExpressionVariableContextProvider variables={{ '@currentGameId': 'widget456' }}>
    {/* Both @sceneId and @currentGameId are available here */}
    <MyComponent />
  </ExpressionVariableContextProvider>
</ExpressionVariableContextProvider>
```

## Game Widget Integration
Game widgets automatically provide expression variables:
- `@currentGameId` - The game widget's ID
- `@gameType` - The game's type/ID

## Examples

### Score Display
```
"Current Score: {game/@currentGameId/score}"
```

### Multi-Game Display
```
"Quiz: {game/quiz-widget-123/score} | Current: {game/@currentGameId/score}"
```

### Game Type Display
```
"Playing: @gameType | Score: {game/@currentGameId/score}"
```

## Migration from Static Paths
Old: `"Score: {game/123456/score}"`
New: `"Score: {game/@currentGameId/score}"`

The new approach works across different game instances without hardcoding IDs.
