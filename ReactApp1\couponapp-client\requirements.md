# Variables Within Expressions - Requirements

## Overview

Extend the existing dynamic values system to support **variables within expressions** that can be resolved using context from game widgets. This allows expressions like `{game/@currentGameId/score}` where `@currentGameId` is replaced with the actual game widget ID from the current context.

## Current State

The dynamic values system already supports:
- Static path expressions: `{game/123456/score}`
- Expression parsing and rendering
- Live value subscriptions
- Game widget value sources registration
- Context providers for dynamic values

## New Functionality

### Variable Syntax
- Variables use `@` prefix: `@currentGameId`, `@widgetId`, `@gameType`
- Variables can be used within dynamic value paths: `{game/@currentGameId/score}`
- Variables are resolved from context providers before path resolution

### Supported Variables

#### Game Context Variables
- `@currentGameId` - The current game widget's ID
- `@currentWidgetId` - Alias for `@currentGameId` 
- `@gameType` - The game type (e.g., 'quiz-game', 'memory-game')

#### Future Extensions
- `@sceneId` - Current scene ID
- `@campaignId` - Current campaign ID
- `@userId` - Current user ID (if available)

## Implementation Plan

### 1. Enhanced Expression Parser

Extend `expression-parser.ts` to:
- Detect variables with `@` prefix within paths
- Extract variable names from expressions
- Support nested variable resolution
- Maintain backward compatibility with static paths

```typescript
// Example parsing results:
parseExpression("{game/@currentGameId/score}")
// Returns: variables: ["@currentGameId"], paths: ["game/{@currentGameId}/score"]

parseExpression("Score: {game/@currentGameId/score} | Lives: {game/@currentGameId/lives}")
// Returns: variables: ["@currentGameId"], paths: ["game/{@currentGameId}/score", "game/{@currentGameId}/lives"]
```

### 2. Variable Context Provider

Create a new context provider that supplies variable values:

```typescript
interface VariableContextValue {
  resolveVariable: (variableName: string) => string | undefined
  availableVariables: Record<string, string>
}

// Usage in game widgets:
<VariableContextProvider 
  variables={{
    '@currentGameId': widgetId,
    '@gameType': gameId,
    '@currentWidgetId': widgetId
  }}
>
  <GameContent />
</VariableContextProvider>
```

### 3. Game Widget Integration

Modify the `GameWidget` component to provide variable context:
- Wrap game components with `VariableContextProvider`
- Pass `widgetId` and `gameId` as context variables
- Ensure context is available to all child components

### 4. Enhanced Dynamic Values System

Update dynamic values hooks to:
- Resolve variables before path resolution
- Support variable-aware expression rendering
- Maintain live updates when variables change

```typescript
// Enhanced hook usage:
const score = useLiveValue("game/@currentGameId/score", 0)
const expression = useDynamicExpression("Your score: {game/@currentGameId/score}")
```

### 5. Editor Support

Extend the value source picker to:
- Show available variables in the UI
- Allow selection of variable-based paths
- Provide autocomplete for variable names
- Validate variable availability in current context

## Technical Details

### Variable Resolution Flow

1. **Parse Expression**: Extract variables and paths from expression
2. **Resolve Variables**: Replace variables with actual values from context
3. **Resolve Paths**: Use resolved paths with existing dynamic values system
4. **Render Result**: Return final rendered expression

### Context Hierarchy

```
DynamicValuesProvider (campaign-level)
  └── VariableContextProvider (widget-level)
      └── GameWidget
          └── GameContext (game-specific)
              └── Game Components
```

### Backward Compatibility

- Existing static paths continue to work unchanged
- No breaking changes to current API
- Variables are opt-in feature

## Use Cases

### Game Score Display
```typescript
// In a text widget within a game:
"Current Score: {game/@currentGameId/score}"
// Resolves to: "Current Score: {game/123456/score}"
```

### Cross-Game References
```typescript
// Reference another game's score:
"Quiz Score: {game/quiz-widget-id/score} | Memory Score: {game/@currentGameId/score}"
```

### Dynamic Leaderboard
```typescript
// In a leaderboard widget:
"Top score for {game/@currentGameId/gameType}: {game/@currentGameId/bestScore}"
```

## Error Handling

- Unknown variables return empty string or configurable fallback
- Invalid variable syntax shows clear error messages
- Editor validation prevents invalid variable usage
- Runtime graceful degradation for missing context

## Testing Strategy

1. **Unit Tests**: Variable parsing and resolution logic
2. **Integration Tests**: Game widget context provision
3. **E2E Tests**: Full expression rendering in campaigns
4. **Editor Tests**: Variable picker and validation

## Migration Path

1. Implement core variable resolution system
2. Add game widget context providers
3. Extend expression parser for variables
4. Update editor UI for variable support
5. Add comprehensive testing
6. Document new functionality

## Future Enhancements

- **Conditional Variables**: `{game/@currentGameId/score > 100 ? "High" : "Low"}`
- **Variable Scoping**: Different variable sets per widget type
- **Custom Variables**: User-defined variables in campaign settings
- **Variable Validation**: Type checking and constraint validation
