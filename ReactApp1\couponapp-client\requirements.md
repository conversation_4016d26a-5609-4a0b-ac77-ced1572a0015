# Expression Variables

## Overview
Extend dynamic values system to support expression variables like `{game/@currentGameId/score}` where `@currentGameId` is replaced with actual widget ID from context.

## Functionality
- **Expression Variable Syntax**: `@currentGameId`, `@gameType` within paths
- **Context Resolution**: Expression variables resolved from game widget context
- **Backward Compatible**: Existing static paths unchanged

## Supported Expression Variables
- `@currentGameId` - Current game widget ID
- `@gameType` - Game type (e.g., 'quiz-game')

## Implementation
1. **Enhanced Expression Parser** - Detect `@` expression variables in paths
2. **Expression Variable Context Provider** - Supply variable values at widget level
3. **Dynamic Values Update** - Resolve expression variables before path resolution

## Example Usage
```typescript
// Expression: "Score: {game/@currentGameId/score}"
// Resolves to: "Score: {game/123456/score}"

<ExpressionVariableContextProvider variables={{ '@currentGameId': widgetId }}>
  <GameContent />
</ExpressionVariableContextProvider>
```

## Context Stacking
Expression variable contexts should merge variables from parent contexts:
- Child variables override parent variables with same key
- Parent variables remain available in child contexts
- Use `useContext()` to access parent context and merge with current variables
