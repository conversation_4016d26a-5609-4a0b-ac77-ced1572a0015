# Variables Within Expressions

## Overview
Extend dynamic values system to support variables like `{game/@currentGameId/score}` where `@currentGameId` is replaced with actual widget ID from context.

## Functionality
- **Variable Syntax**: `@currentGameId`, `@gameType` within paths
- **Context Resolution**: Variables resolved from game widget context
- **Backward Compatible**: Existing static paths unchanged

## Supported Variables
- `@currentGameId` - Current game widget ID
- `@gameType` - Game type (e.g., 'quiz-game')

## Implementation
1. **Enhanced Expression Parser** - Detect `@` variables in paths
2. **Variable Context Provider** - Supply variable values at widget level
3. **Game Widget Integration** - Wrap games with context provider
4. **Dynamic Values Update** - Resolve variables before path resolution

## Example Usage
```typescript
// Expression: "Score: {game/@currentGameId/score}"
// Resolves to: "Score: {game/123456/score}"

<VariableContextProvider variables={{ '@currentGameId': widgetId }}>
  <GameContent />
</VariableContextProvider>
```
