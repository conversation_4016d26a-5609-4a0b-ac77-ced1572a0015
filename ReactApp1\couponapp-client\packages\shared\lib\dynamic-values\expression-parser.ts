/**
 * Expression parser for dynamic content in text widgets
 * Supports syntax like: "Score: {game/123456/score} | Lives: {game/123456/lives}"
 */

export interface ParsedExpression {
  type: 'text' | 'variable'
  content: string
  path?: string  // Only for variable type
}

export interface ExpressionParseResult {
  segments: ParsedExpression[]
  variables: string[]  // All unique variable paths found
  expressionVariables: string[]  // All unique expression variables found (e.g., @currentGameId)
  isValid: boolean
  errors: string[]
}

/**
 * Parse a text expression containing dynamic value references
 * @param expression - Text with embedded variables like "Score: {game/123456/score}"
 * @returns Parsed result with segments and metadata
 */
export function parseExpression(expression: string): ExpressionParseResult {
  const segments: ParsedExpression[] = []
  const variables: string[] = []
  const expressionVariables: string[] = []
  const errors: string[] = []
  let isValid = true

  if (!expression) {
    return { segments: [], variables: [], expressionVariables: [], isValid: true, errors: [] }
  }
  
  // Regex to match variables: {path/to/value}
  const variableRegex = /\{([^}]+)\}/g
  let lastIndex = 0
  let match
  
  while ((match = variableRegex.exec(expression)) !== null) {
    const [fullMatch, path] = match
    const matchStart = match.index
    const matchEnd = match.index + fullMatch.length
    
    // Add text segment before this variable (if any)
    if (matchStart > lastIndex) {
      const textContent = expression.slice(lastIndex, matchStart)
      if (textContent) {
        segments.push({
          type: 'text',
          content: textContent
        })
      }
    }
    
    // Validate the path
    const trimmedPath = path.trim()
    if (!trimmedPath) {
      errors.push(`Empty variable path at position ${matchStart}`)
      isValid = false
      continue
    }
    
    // Extract expression variables from path (e.g., @currentGameId)
    const pathExpressionVariables = extractExpressionVariablesFromPath(trimmedPath)
    pathExpressionVariables.forEach(expVar => {
      if (!expressionVariables.includes(expVar)) {
        expressionVariables.push(expVar)
      }
    })

    // Basic path validation (should have at least category/widgetId/property)
    const pathParts = trimmedPath.split('/')
    if (pathParts.length < 3) {
      errors.push(`Invalid path format '${trimmedPath}' - expected format: category/widgetId/property`)
      isValid = false
      continue
    }

    // Add variable segment
    segments.push({
      type: 'variable',
      content: fullMatch,
      path: trimmedPath
    })

    // Track unique variables
    if (!variables.includes(trimmedPath)) {
      variables.push(trimmedPath)
    }
    
    lastIndex = matchEnd
  }
  
  // Add remaining text after last variable (if any)
  if (lastIndex < expression.length) {
    const textContent = expression.slice(lastIndex)
    if (textContent) {
      segments.push({
        type: 'text',
        content: textContent
      })
    }
  }
  
  // If no variables found, treat entire string as text
  if (segments.length === 0 && expression) {
    segments.push({
      type: 'text',
      content: expression
    })
  }
  
  return {
    segments,
    variables,
    expressionVariables,
    isValid,
    errors
  }
}

/**
 * Render an expression by replacing variables with their current values
 * @param expression - The expression string
 * @param valueGetter - Function to get current value for a path
 * @param fallbackValue - Value to use when a variable is not found
 * @returns Rendered string with variables replaced
 */
export function renderExpression(
  expression: string,
  valueGetter: (path: string) => any,
  fallbackValue: string = '—'
): string {
  const parsed = parseExpression(expression)

  if (!parsed.isValid) {
    return expression // Return original if invalid
  }

  return parsed.segments.map(segment => {
    if (segment.type === 'text') {
      return segment.content
    } else if (segment.type === 'variable' && segment.path) {
      const value = valueGetter(segment.path)

      // Handle different value types
      if (value === null || value === undefined) {
        return fallbackValue
      }

      if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No'
      }

      if (typeof value === 'number') {
        return value.toString()
      }

      if (typeof value === 'object') {
        return JSON.stringify(value)
      }

      return String(value)
    }

    return segment.content
  }).join('')
}

/**
 * Render an expression with expression variable resolution
 * @param expression - The expression string
 * @param valueGetter - Function to get current value for a path
 * @param variableResolver - Function to resolve expression variables
 * @param fallbackValue - Value to use when a variable is not found
 * @returns Rendered string with variables replaced
 */
export function renderExpressionWithVariables(
  expression: string,
  valueGetter: (path: string) => any,
  variableResolver: (variableName: string) => string | undefined,
  fallbackValue: string = '—'
): string {
  const parsed = parseExpression(expression)

  if (!parsed.isValid) {
    return expression // Return original if invalid
  }

  return parsed.segments.map(segment => {
    if (segment.type === 'text') {
      return segment.content
    } else if (segment.type === 'variable' && segment.path) {
      // Resolve expression variables in the path first
      const resolvedPath = resolveExpressionVariablesInPath(segment.path, variableResolver)
      const value = valueGetter(resolvedPath)

      // Handle different value types
      if (value === null || value === undefined) {
        return fallbackValue
      }

      if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No'
      }

      if (typeof value === 'number') {
        return value.toString()
      }

      if (typeof value === 'object') {
        return JSON.stringify(value)
      }

      return String(value)
    }

    return segment.content
  }).join('')
}

/**
 * Validate an expression and return detailed validation results
 * @param expression - The expression to validate
 * @param availablePaths - Array of available value paths for validation
 * @returns Validation result with errors and warnings
 */
export function validateExpression(
  expression: string,
  availablePaths: string[] = []
): {
  isValid: boolean
  errors: string[]
  warnings: string[]
  unknownPaths: string[]
} {
  const parsed = parseExpression(expression)
  const warnings: string[] = []
  const unknownPaths: string[] = []
  
  // Check if referenced paths exist in available sources
  for (const path of parsed.variables) {
    if (availablePaths.length > 0 && !availablePaths.includes(path)) {
      unknownPaths.push(path)
      warnings.push(`Path '${path}' is not available in current campaign`)
    }
  }
  
  return {
    isValid: parsed.isValid,
    errors: parsed.errors,
    warnings,
    unknownPaths
  }
}

/**
 * Extract all variable paths from an expression
 * @param expression - The expression string
 * @returns Array of unique variable paths
 */
export function extractVariablePaths(expression: string): string[] {
  const parsed = parseExpression(expression)
  return parsed.variables
}

/**
 * Check if an expression contains any dynamic variables
 * @param expression - The expression string
 * @returns True if expression contains variables
 */
export function hasDynamicContent(expression: string): boolean {
  return extractVariablePaths(expression).length > 0
}

/**
 * Replace a variable path in an expression with a new path
 * @param expression - The original expression
 * @param oldPath - The path to replace
 * @param newPath - The new path
 * @returns Updated expression
 */
export function replaceVariablePath(
  expression: string,
  oldPath: string,
  newPath: string
): string {
  const oldVariable = `{${oldPath}}`
  const newVariable = `{${newPath}}`
  return expression.replace(new RegExp(escapeRegExp(oldVariable), 'g'), newVariable)
}

/**
 * Extract expression variables from a path (e.g., @currentGameId from "game/@currentGameId/score")
 * @param path - The path to scan for expression variables
 * @returns Array of expression variables found
 */
export function extractExpressionVariablesFromPath(path: string): string[] {
  const expressionVariableRegex = /@[a-zA-Z][a-zA-Z0-9_]*/g
  const matches = path.match(expressionVariableRegex)
  return matches || []
}

/**
 * Resolve expression variables in a path using a variable resolver
 * @param path - Path containing expression variables (e.g., "game/@currentGameId/score")
 * @param variableResolver - Function to resolve variable names to values
 * @returns Path with variables resolved (e.g., "game/123456/score")
 */
export function resolveExpressionVariablesInPath(
  path: string,
  variableResolver: (variableName: string) => string | undefined
): string {
  return path.replace(/@[a-zA-Z][a-zA-Z0-9_]*/g, (match) => {
    const resolved = variableResolver(match)
    return resolved || match // Keep original if not resolved
  })
}

/**
 * Extract all expression variables from an expression
 * @param expression - The expression string
 * @returns Array of unique expression variables
 */
export function extractExpressionVariables(expression: string): string[] {
  const parsed = parseExpression(expression)
  return parsed.expressionVariables
}

/**
 * Escape special regex characters in a string
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}
