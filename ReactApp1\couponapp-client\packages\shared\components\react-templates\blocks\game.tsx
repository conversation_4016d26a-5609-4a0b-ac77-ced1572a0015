import { useGameModule } from "@repo/shared/lib/hooks/useGameModule";
import { Widget, WidgetRenderingContextNew } from "@repo/shared/lib/types/editor";
import { addWidgetMetadata } from "@repo/shared/lib/widget-metadata";
import { addPropertyControlsNew, ControlType } from "../../editor/property-controls-new";
import React, { Suspense, useMemo } from "react";

export const GameWidget: React.FC<
  WidgetRenderingContextNew<any>
> = ({
  widgetId,
  settings,
  editorContext,
  resolveAssetUrl,
  children
}) => {
  const isEditorMode = editorContext?.isEditorMode ?? false;

  const style: React.CSSProperties = {
    overflow: "hidden",
    margin: settings.margin,
    padding: settings.padding,
    borderRadius: settings.border?.radius,
    width: settings.width,
    height: settings.height,
  };

  const { gameModule, error } = useGameModule(settings.gameId);

  // Determine which component to use based on editor mode
  const GameComponent = gameModule
    ? isEditorMode
      ? gameModule.previewScene
      : gameModule.runtimeComponent
    : null;

  if (error) {
    return (
      <div style={style}>
        <div>Error loading game: {(error as Error).message}</div>
      </div>
    );
  }

  return (
    <div style={style} className="">
       <Suspense fallback={<div>Loading game...</div>}>
        {GameComponent && (
          <GameComponent
            widgetId={widgetId}
            config={settings.gameConfig}
            inputEnabled={!isEditorMode}
            isPreview={isEditorMode}
            resolveAssetUrl={resolveAssetUrl}
            children={children}
          />
        )}
      </Suspense> 

    </div>
  );
};

addWidgetMetadata(GameWidget, {
  componentName: "GameWidget",
  displayName: "Game",
  type: "block",
});

addPropertyControlsNew(GameWidget, {
  gameId: {
    type: ControlType.Text,
    title: "Game ID",
    defaultValue: ""
  },

  gameSkin: {
    type: ControlType.GameSkinSelector,
    title: "Game Skin",
  },

  gameConfig: {
    type: ControlType.GameConfigEditor,
    title: "Game Config",
    defaultValue: {}
  },

  // Alignment
  horizontalAlignment: {
    type: ControlType.Enum,
    title: "Horizontal Alignment",
    defaultValue: "center",
    options: [
      { label: "Left", value: "left" },
      { label: "Center", value: "center" },
      { label: "Right", value: "right" }
    ]
  },
  verticalAlignment: {
    type: ControlType.Enum,
    title: "Vertical Alignment",
    defaultValue: "center",
    options: [
      { label: "Top", value: "top" },
      { label: "Center", value: "center" },
      { label: "Bottom", value: "bottom" }
    ]
  },

  // Size
  width: {
    type: ControlType.Text,
    title: "Width",
    defaultValue: "600px"
  },
  height: {
    type: ControlType.Text,
    title: "Height",
    defaultValue: "600px"
  },

  // Layout
  padding: {
    type: ControlType.Padding,
    title: "Padding",
    defaultValue: "0px"
  },
  margin: {
    type: ControlType.Margin,
    title: "Margin",
    defaultValue: "0px"
  },

  // Border
  border: {
    type: ControlType.Border,
    title: "Border",
    defaultValue: {
      width: "0px",
      style: "none",
      color: "#000000",
      radius: "0px"
    }
  }
});
