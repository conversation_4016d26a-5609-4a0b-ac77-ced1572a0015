import React from 'react';
import { ExpressionVariableContextProvider } from './context';
import { useDynamicExpressionWithVariables } from '../hooks/useDynamicExpression';

/**
 * Example component demonstrating expression variables usage
 */
function ExampleGameScore() {
  // This would render: "Your score: {game/123456/score}"
  const scoreText = useDynamicExpressionWithVariables(
    "Your score: {game/@currentGameId/score}",
    "0"
  );
  
  return <div>{scoreText}</div>;
}

/**
 * Example usage in a game widget context
 */
export function ExpressionVariablesExample() {
  const gameWidgetId = "123456";
  const gameType = "quiz-game";
  
  return (
    <ExpressionVariableContextProvider 
      variables={{
        '@currentGameId': gameWidgetId,
        '@gameType': gameType
      }}
    >
      <div>
        <h3>Expression Variables Test</h3>
        <ExampleGameScore />
        
        {/* Test nested contexts */}
        <ExpressionVariableContextProvider 
          variables={{
            '@sceneId': 'scene789'
          }}
        >
          <div>
            <p>Game ID: @currentGameId should resolve to {gameWidgetId}</p>
            <p>Game Type: @gameType should resolve to {gameType}</p>
            <p>Scene ID: @sceneId should resolve to scene789</p>
          </div>
        </ExpressionVariableContextProvider>
      </div>
    </ExpressionVariableContextProvider>
  );
}

/**
 * Test the expression parser functions directly
 */
export function testExpressionVariables() {
  const { 
    extractExpressionVariablesFromPath,
    resolveExpressionVariablesInPath,
    extractExpressionVariables 
  } = require('../expression-parser');
  
  console.log('Testing expression variables...');
  
  // Test 1: Extract variables from path
  const path1 = "game/@currentGameId/score";
  const variables1 = extractExpressionVariablesFromPath(path1);
  console.log(`Path: ${path1}, Variables: ${JSON.stringify(variables1)}`);
  // Expected: ["@currentGameId"]
  
  // Test 2: Resolve variables in path
  const resolver = (varName: string) => {
    if (varName === '@currentGameId') return '123456';
    if (varName === '@gameType') return 'quiz-game';
    return undefined;
  };
  
  const resolvedPath = resolveExpressionVariablesInPath(path1, resolver);
  console.log(`Resolved path: ${resolvedPath}`);
  // Expected: "game/123456/score"
  
  // Test 3: Extract variables from full expression
  const expression = "Score: {game/@currentGameId/score} | Type: {game/@gameType/name}";
  const expressionVars = extractExpressionVariables(expression);
  console.log(`Expression: ${expression}`);
  console.log(`Expression variables: ${JSON.stringify(expressionVars)}`);
  // Expected: ["@currentGameId", "@gameType"]
  
  console.log('Expression variables tests completed!');
}
