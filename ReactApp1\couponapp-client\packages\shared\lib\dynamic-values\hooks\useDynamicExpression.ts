import { useMemo } from 'react';
import { useLiveValues } from './useLiveValue';
import {
  renderExpression,
  renderExpressionWithVariables,
  extractVariablePaths,
  extractExpressionVariables,
  resolveExpressionVariablesInPath
} from '../expression-parser';
import { useExpressionVariables } from '../expression-variables';

/**
 * Hook to render dynamic text expressions with live value updates
 * @param expression - The expression string with variables like "Score: {game/123456/score}"
 * @param fallbackValue - Value to use when a variable is not found
 * @returns Rendered string with variables replaced by live values
 */
export function useDynamicExpression(expression: string, fallbackValue: string = ''): string {
  // Extract all variable paths from the expression
  const variablePaths = useMemo(() => {
    return extractVariablePaths(expression);
  }, [expression]);

  // Subscribe to all dynamic values used in the expression
  const dynamicValues = useLiveValues(variablePaths);

  // Render the expression with current dynamic values
  const renderedText = useMemo(() => {
    return renderExpression(
      expression,
      (path: string) => dynamicValues[path],
      fallbackValue
    );
  }, [expression, dynamicValues, fallbackValue]);

  return renderedText;
}

/**
 * Enhanced hook to render dynamic text expressions with expression variable support
 * @param expression - The expression string with variables like "Score: {game/@currentGameId/score}"
 * @param fallbackValue - Value to use when a variable is not found
 * @returns Rendered string with variables replaced by live values
 */
export function useDynamicExpressionWithVariables(expression: string, fallbackValue: string = ''): string {
  const { resolveVariable } = useExpressionVariables();

  // Extract expression variables and resolve paths
  const resolvedPaths = useMemo(() => {
    const originalPaths = extractVariablePaths(expression);
    return originalPaths.map(path =>
      resolveExpressionVariablesInPath(path, resolveVariable)
    );
  }, [expression, resolveVariable]);

  // Subscribe to all resolved dynamic values
  const dynamicValues = useLiveValues(resolvedPaths);

  // Render the expression with expression variable resolution
  const renderedText = useMemo(() => {
    return renderExpressionWithVariables(
      expression,
      (path: string) => dynamicValues[path],
      resolveVariable,
      fallbackValue
    );
  }, [expression, dynamicValues, resolveVariable, fallbackValue]);

  return renderedText;
}
