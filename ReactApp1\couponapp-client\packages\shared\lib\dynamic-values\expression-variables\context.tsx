import React, { createContext, useContext, useMemo, useCallback } from 'react'
import { ExpressionVariableContextValue, ExpressionVariableProviderProps } from './types'

const ExpressionVariableContext = createContext<ExpressionVariableContextValue | null>(null)

export function ExpressionVariableContextProvider({ 
  variables, 
  children 
}: ExpressionVariableProviderProps) {
  const parentContext = useContext(ExpressionVariableContext)
  
  // Merge parent variables with current variables (child overrides parent)
  const mergedVariables = useMemo(() => ({
    ...parentContext?.availableVariables, // Parent variables first
    ...variables // Child variables override parent if same key
  }), [parentContext?.availableVariables, variables])
  
  const resolveVariable = useCallback((variableName: string): string | undefined => {
    return mergedVariables[variableName]
  }, [mergedVariables])
  
  const contextValue: ExpressionVariableContextValue = useMemo(() => ({
    resolveVariable,
    availableVariables: mergedVariables
  }), [resolveVariable, mergedVariables])
  
  return (
    <ExpressionVariableContext.Provider value={contextValue}>
      {children}
    </ExpressionVariableContext.Provider>
  )
}

export function useExpressionVariables(): ExpressionVariableContextValue {
  const context = useContext(ExpressionVariableContext)
  
  if (!context) {
    // Return empty context if no provider found (graceful degradation)
    return {
      resolveVariable: () => undefined,
      availableVariables: {}
    }
  }
  
  return context
}
